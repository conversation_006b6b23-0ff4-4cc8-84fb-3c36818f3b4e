<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>Pure CSS Icon Sidebar</title>
<style>
  :root{
    --bg: #f6f7fb;                 /* page background */
    --rail-bg: #ffffff;            /* rail background */
    --rail-border: rgba(0,0,0,.06);
    --pill: #eef0f7;               /* item background */
    --pill-hover: #e9ebf4;
    --active: #1f1f23;             /* active item background */
    --icon: #1b1e2b;               /* default icon */
    --icon-active: #ffffff;        /* active icon */
    --text: #1f2233;               /* label text */
    --rail-collapsed: 64px;        /* collapsed rail width */
    --rail-expanded: 220px;        /* expanded rail width */
    --item-size: 44px;             /* circle size */
  }

  /* page baseline */
  html, body { height: 100%; }
  body{
    margin: 0; background: var(--bg);
    font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  }

  /* sidebar rail */
  .side-rail{
    position: fixed; inset: 0 auto 0 0;       /* left full-height */
    width: var(--rail-collapsed);
    display: flex; flex-direction: column; align-items: center;
    gap: 18px; padding: 18px 10px;
    background: var(--rail-bg);
    border-right: 1px solid var(--rail-border);
    transition: width .24s ease;
  }
  /* expand the rail to reveal labels */
  .side-rail:hover{ width: var(--rail-expanded); }

  /* brand at the very top */
  .brand{ height: 28px; display: grid; place-items: center; }
  .brand svg{ width: 22px; height: 22px; }

  /* stacked items */
  .stack{ display: flex; flex-direction: column; gap: 12px; width: 100%; align-items: center; }

  .item{
    display: flex; align-items: center; gap: 12px;
    width: var(--item-size); height: var(--item-size);
    padding: 0 14px; border-radius: 999px; border: 1px solid var(--rail-border);
    background: var(--pill);
    overflow: hidden;               /* hide label while collapsed */
    text-decoration: none;          /* anchors */
    transition: width .22s ease, background .18s ease, box-shadow .18s ease;
    box-shadow: 0 1px 0 rgba(0,0,0,.02);
  }
  .item:focus{ outline: none; box-shadow: 0 0 0 2px rgba(90,85,255,.25); }

  /* expand individual pills when rail is hovered */
  .side-rail:hover .item{ width: 180px; justify-content: flex-start; }

  .item .icon{
    width: 20px; height: 20px; flex: 0 0 20px;
    stroke: var(--icon); stroke-width: 2; stroke-linecap: round; stroke-linejoin: round; fill: none;
  }
  .item .label{
    font-weight: 600; font-size: 12px; letter-spacing: .4px; color: var(--text);
    white-space: nowrap; opacity: 0; transform: translateX(8px);
    transition: opacity .16s ease .04s, transform .16s ease .04s;
  }
  /* reveal labels on hover of the rail */
  .side-rail:hover .item .label{ opacity: 1; transform: translateX(0); }

  /* states */
  .item:hover{ background: var(--pill-hover); }
  .item.active{ background: var(--active); border-color: var(--active); }
  .item.active .icon{ stroke: var(--icon-active); }
  .item.active .label{ color: var(--icon-active); }

  /* optional footer group */
  .spacer{ flex: 1; }
  .muted{ opacity: .65; }
</style>
</head>
<body>
  <aside class="side-rail" aria-label="Primary">
    <div class="brand" aria-hidden="true">
      <!-- simple stylized A mark -->
      <svg viewBox="0 0 24 24"><path d="M4 20L12 3l8 17" stroke="#1b1e2b" stroke-width="2" fill="none" stroke-linecap="round"/><path d="M8 13h8" stroke="#1b1e2b" stroke-width="2" fill="none" stroke-linecap="round"/></svg>
    </div>

    <nav class="stack">
      <a class="item active" href="#" aria-label="Home">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M3 10l9-7 9 7"/>
          <path d="M5 10v10h14V10"/>
        </svg>
        <span class="label">Home</span>
      </a>

      <a class="item" href="#" aria-label="Clipboard">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M9 4h6a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"/>
          <path d="M9 2h6v4H9z"/>
        </svg>
        <span class="label">Clipboard</span>
      </a>

      <a class="item" href="#" aria-label="Docs">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M14 2H7a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V7z"/>
          <path d="M14 2v5h5"/>
        </svg>
        <span class="label">Documents</span>
      </a>

      <a class="item" href="#" aria-label="Chat">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M21 15a4 4 0 0 1-4 4H8l-5 3V6a4 4 0 0 1 4-4h10a4 4 0 0 1 4 4z"/>
        </svg>
        <span class="label">Messages</span>
      </a>

      <a class="item" href="#" aria-label="Settings">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M12 15.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
          <path d="M19.4 15a1 1 0 0 0 .2 1.1l.1.1a2 2 0 0 1-2.8 2.8l-.1-.1a1 1 0 0 0-1.1-.2 7.8 7.8 0 0 1-1 .4 1 1 0 0 0-.7.9V21a2 2 0 0 1-4 0v-.1a1 1 0 0 0-.7-.9 8.2 8.2 0 0 1-1-.4 1 1 0 0 0-1.1.2l-.1.1A2 2 0 1 1 4.3 16l.1-.1a1 1 0 0 0 .2-1.1 7.4 7.4 0 0 1-.4-1 1 1 0 0 0-.9-.7H3a2 2 0 0 1 0-4h.1a1 1 0 0 0 .9-.7 7.4 7.4 0 0 1 .4-1 1 1 0 0 0-.2-1.1l-.1-.1A2 2 0 1 1 6.2 4.3l.1.1a1 1 0 0 0 1.1.2c.3-.1.6-.3 1-.4a1 1 0 0 0 .7-.9V3a2 2 0 0 1 4 0v.1a1 1 0 0 0 .7.9c.3.1.6.3 1 .4a1 1 0 0 0 1.1-.2l.1-.1A2 2 0 0 1 20.7 6l-.1.1a1 1 0 0 0-.2 1.1c.1.3.3 .6.4 1a1 1 0 0 0 .9.7H21a2 2 0 0 1 0 4h-.1a1 1 0 0 0-.9 .7c-.1 .3-.3 .7-.4 1z"/>
        </svg>
        <span class="label">Settings</span>
      </a>
    </nav>

    <div class="spacer"></div>

    <nav class="stack">
      <a class="item muted" href="#" aria-label="Help">
        <svg class="icon" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M9 9a3 3 0 1 1 6 0c0 2-3 2-3 5"/>
          <path d="M12 18h.01"/>
          <circle cx="12" cy="12" r="10"/>
        </svg>
        <span class="label">Help</span>
      </a>
    </nav>
  </aside>

  <!-- demo content to the right -->
  <main style="margin-left: var(--rail-collapsed); padding: 32px;">
    <h1>Content Area</h1>
    <p>Hover the left rail to reveal labels. No JavaScript used.</p>
  </main>
</body>
</html>
