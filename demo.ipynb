import json
with open("sessions_data/sessions.json", "r") as sessions_log_file:
    data = json.load(sessions_log_file)
    data[12235] = {"status":"running", "progress": 0}

with open("sessions_data/sessions.json", "w") as sessions_log_file:
    json.dump(data, sessions_log_file, indent=4)


with open("sessions_data/sessions.json", "r") as sessions_log_file:
    data = json.load(sessions_log_file)
    data["12235"]["progress"] = 50

with open("sessions_data/sessions.json", "w") as sessions_log_file:
    json.dump(data, sessions_log_file, indent=4)


import os

def read_all_folders():
    folder = r"C:\D_Partition\Compare\Work"
    
    for root, dirs, files in os.walk(folder):
        print(f"Folder: {root}")
        for file in files:
            print(f"  {file}")





import subprocess
import sys

process_1 = subprocess.Popen(
    [sys.executable, "function.py"],
    capture_output=True,
    text=True
)

import time
print(time.strftime("%H:%M"))


import pandas as pd
df = pd.read_excel("tools_data.xlsx")


tools_dict = {}
for _, row in df.iterrows():
    tool_id = row['ID']
    if tool_id not in tools_dict:
        tools_dict[tool_id] = []
    
    tool_info = {
        'Name': row['Name'],
        'Description': row['Description'],
        'Tool_Name': row['Tool_Name'],
        "Category": row['Category'],
        'Category_ID': row['Category_ID']
    }
    tools_dict[tool_id].append(tool_info)

print(tools_dict)