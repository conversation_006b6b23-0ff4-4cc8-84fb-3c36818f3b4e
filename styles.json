{"version": "1.0.0", "generated_at": "2025-08-16T11:40:46.981709Z", "name": "health-dashboard-light", "description": "Design tokens & component styles inspired by a clean, airy health dashboard with yellow accent, soft gray surfaces, rounded cards, and pill controls.", "meta": {"varPrefix": "--ui-", "cssVariables": true, "font_fallbacks": "Inter, Noto Sans Arabic, Segoe UI, Roboto, Helvetica Neue, Arial, system-ui, sans-serif", "source_reference": "screenshot provided by user"}, "theme": {"mode": "light", "radius_scale_px": [4, 8, 12, 16, 20, 24, 28, 9999]}, "colors": {"brand": {"primary": "#FFC928", "primary-strong": "#FFB800", "primary-weak": "#FFE9A6"}, "neutral": {"bg": "#F7F7F8", "surface": "#FFFFFF", "surface-alt": "#F3F4F6", "border": "rgba(0,0,0,0.08)", "muted": "#8B8D97", "text": "#161619", "text-soft": "#40424A", "icon": "#1C1B29"}, "status": {"success": "#22C55E", "warning": "#F59E0B", "danger": "#EF4444", "info": "#0EA5E9"}, "charts": {"bar-1": "#111113", "bar-2": "#6B6F76", "bar-accent": "#FFC928", "line": "#1C1B29", "grid": "rgba(0,0,0,0.06)", "dot": "#FFC928"}, "elevation": {"shadow": "rgba(17, 17, 19, 0.06)", "shadow-strong": "rgba(17, 17, 19, 0.10)"}}, "typography": {"fontFamily": "<PERSON>, <PERSON><PERSON>, var(--ui-font-fallbacks)", "scale": {"xxl": {"size": 42, "line": 52, "weight": 800}, "xl": {"size": 28, "line": 36, "weight": 700}, "lg": {"size": 20, "line": 28, "weight": 700}, "md": {"size": 16, "line": 24, "weight": 600}, "sm": {"size": 14, "line": 20, "weight": 500}, "xs": {"size": 12, "line": 16, "weight": 500}}, "numerals": {"bigMetric": {"size": 36, "line": 44, "weight": 800, "tabular": true}, "stat": {"size": 24, "line": 32, "weight": 700, "tabular": true}}, "letterSpacing": {"tight": -0.2, "normal": 0, "wide": 0.2}}, "spacing": {"unit": 4, "scale": [0, 2, 4, 8, 12, 16, 20, 24, 28, 32, 40, 48, 56, 64]}, "radii": {"xs": 8, "sm": 12, "md": 16, "lg": 20, "xl": 24, "2xl": 28, "pill": 9999}, "shadows": {"sm": "0 1px 2px rgba(17,17,19,0.04), 0 1px 1px rgba(17,17,19,0.06)", "md": "0 8px 20px rgba(17,17,19,0.06)", "lg": "0 14px 32px rgba(17,17,19,0.10)"}, "borders": {"thin": "1px solid rgba(0,0,0,0.08)", "thick": "2px solid rgba(0,0,0,0.12)", "focus": "2px solid #FFC928"}, "components": {"card": {"bg": "{colors.neutral.surface}", "radius": "{radii.xl}", "shadow": "{shadows.md}", "padding": "{spacing.scale[9]}", "border": "{borders.thin}", "header": {"gap": "{spacing.scale[4]}", "titleColor": "{colors.neutral.text}"}}, "sidebar": {"width": 72, "bg": "{colors.neutral.surface}", "radius": "{radii.lg}", "shadow": "{shadows.sm}", "activeDot": "{colors.brand.primary}", "icon": {"size": 24, "default": "{colors.neutral.icon}", "active": "{colors.brand.primary}"}, "rail": {"gap": "{spacing.scale[6]}", "paddingY": "{spacing.scale[8]}"}}, "navbar": {"height": 64, "bg": "{colors.neutral.surface}", "shadow": "{shadows.sm}", "radius": "{radii.lg}", "badgeBg": "{colors.brand.primary}"}, "button": {"radius": "{radii.pill}", "height": 40, "paddingX": 16, "border": "{borders.thin}", "font": "{typography.scale.sm}", "variants": {"primary": {"bg": "{colors.brand.primary}", "fg": "#111113", "hoverBg": "{colors.brand.primary-strong}", "shadow": "{shadows.sm}", "border": "transparent"}, "secondary": {"bg": "{colors.neutral.surface}", "fg": "{colors.neutral.text}", "hoverBg": "{colors.neutral.surface-alt}", "border": "{borders.thin}"}, "ghost": {"bg": "transparent", "fg": "{colors.neutral.text-soft}", "hoverBg": "{colors.neutral.surface-alt}", "border": "transparent"}, "pillToggle": {"bg": "{colors.neutral.surface}", "fg": "{colors.neutral.text}", "activeBg": "{colors.neutral.text}", "activeFg": "#FFFFFF"}}}, "input": {"bg": "{colors.neutral.surface}", "fg": "{colors.neutral.text}", "placeholder": "{colors.neutral.muted}", "radius": "{radii.lg}", "border": "{borders.thin}", "focus": "{borders.focus}", "height": 44, "paddingX": 16}, "badge": {"radius": "{radii.pill}", "paddingX": 10, "height": 24, "fg": "{colors.neutral.text}", "bg": "{colors.brand.primary-weak}"}, "checkbox": {"size": 18, "radius": 6, "border": "{borders.thin}", "checkedBg": "{colors.brand.primary}", "icon": "#111113"}, "toggle": {"track": {"width": 52, "height": 28, "radius": 9999, "bg": "{colors.neutral.surface-alt}"}, "thumb": {"size": 22, "bg": "#FFFFFF", "shadow": "{shadows.sm}"}, "on": {"trackBg": "{colors.brand.primary}"}}, "progress": {"height": 10, "radius": "{radii.pill}", "bg": "{colors.neutral.surface-alt}", "bar": "{colors.brand.primary}"}, "stat": {"value": "{typography.numerals.stat}", "unitColor": "{colors.neutral.muted}", "delta": {"upColor": "{colors.status.success}", "downColor": "{colors.status.danger}", "neutralColor": "{colors.neutral.muted}"}}, "chart": {"gridColor": "{colors.charts.grid}", "barColors": ["{colors.charts.bar-1}", "{colors.charts.bar-2}", "{colors.charts.bar-accent}"], "lineColor": "{colors.charts.line}", "dotColor": "{colors.charts.dot}", "radius": 8}, "kpi": {"tileBg": "{colors.neutral.surface}", "tileRadius": "{radii.xl}", "ringAccent": "{colors.brand.primary}"}}, "exports": {"css": true, "json": true, "tailwind": {"prefix": "ui", "map": {"colors": {"primary": "{colors.brand.primary}", "bg": "{colors.neutral.bg}", "surface": "{colors.neutral.surface}", "muted": "{colors.neutral.muted}", "text": "{colors.neutral.text}"}, "radius": {"xl": "{radii.xl}"}, "shadow": {"md": "{shadows.md}"}}}}}