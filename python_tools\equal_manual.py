import os
import re
import subprocess as sp
from time import sleep
import concurrent.futures
import pdfplumber
from tqdm import tqdm
from rich.console import Console
from rich.markdown import Markdown
import fitz
from multiprocessing import freeze_support
from datetime import date
import sys
import json


class Word:
    def __init__(self, word_text, word_start, word_end, word_up, word_down, word_page):
        self.word_text = word_text
        self.word_start = word_start
        self.word_end = word_end
        self.word_up = word_up
        self.word_down = word_down
        self.word_page = word_page


def extract_text_from_pdf(file_path):
    doc = pdfplumber.open(file_path)
    text = "\n".join([page.extract_text_simple() for page in doc.pages])
    text = re.sub(' +', ' ', text)
    return text.lower().strip().replace("  ", " ")


def fitz_extract_all_words(link):
    start_index, end_index, up_index, down_index, text_index = 0, 2, 1, 3, 4

    word_properties_list = []
    list_of_lines = []

    link = link.removesuffix('\n')
    doc_1 = fitz.open(link.removesuffix('\n'))

    for page_index, page in enumerate(doc_1):
        for ext_word in page.get_text("words"):
            word_properties_list.append(
                Word(ext_word[text_index], ext_word[start_index], ext_word[end_index], ext_word[up_index],
                     ext_word[down_index], page_index))

        line_list_of_words = []
        found_words_list = []

        for fixed_word in word_properties_list:
            if fixed_word in found_words_list:
                continue
            for looping_word in word_properties_list:
                if (looping_word.word_up - 4 <= fixed_word.word_up <= looping_word.word_up + 4
                        or looping_word.word_down - 4 <= fixed_word.word_down <= looping_word.word_down + 4):
                    line_list_of_words.append(looping_word)
                    found_words_list.append(looping_word)

            line_list_of_words.sort(key=lambda x: x.word_start)
            list_of_lines.append(line_list_of_words)

            line_list_of_words = []
        word_properties_list = []
    lines_list = []
    for line in list_of_lines:
        lines_list.append(" ".join([word.word_text for word in line]))

    return "\n".join(lines_list)


def count_of_images(link):
    doc = fitz.open(link)
    num_images = 0
    for page_index in range(len(doc)):
        image_list = doc[page_index].get_images()

        # printing number of images found in this page
        if image_list:
            num_images += len(image_list)

    return num_images


def count_of_pages(link):
    doc = fitz.open(link)
    return doc.page_count


def compare_lines(doc_text1: str, doc_text2: str, lines_limit: int):
    lines_list1 = set(doc_text1.split('\n'))
    lines_list2 = set(doc_text2.split("\n"))

    doc_changed_lines_1 = list(lines_list1 - lines_list2)
    doc_changed_lines_2 = list(lines_list2 - lines_list1)
    # get number of lines changed
    for line in doc_changed_lines_1:
        if line.strip() == '':
            doc_changed_lines_1.remove(line)
    for line in doc_changed_lines_2:
        if line.strip() == '':
            doc_changed_lines_2.remove(line)

    num_lines_1 = len(doc_changed_lines_1)
    num_lines_2 = len(doc_changed_lines_2)

    if len(doc_changed_lines_1) > lines_limit or len(doc_changed_lines_2) > lines_limit:
        doc_changed_lines_2 = doc_changed_lines_1 = ['']
        compare_line_status = "Not Equal"

    elif len(doc_changed_lines_1) != len(doc_changed_lines_2):
        compare_line_status = "Not Equal"

    else:
        removed_date_lines_1, removed_date_lines_2 = remove_date(doc_changed_lines_1, doc_changed_lines_2)
        compare_line_status = 'Equal' if removed_date_lines_1 == removed_date_lines_2 else "Not Equal"
    return doc_changed_lines_1, doc_changed_lines_2, num_lines_1, num_lines_2, compare_line_status


def arrange_lines(doc_changed_lines_1, doc_changed_lines_2, lines_limit):
    if len(doc_changed_lines_1) < lines_limit:
        for x in range(len(doc_changed_lines_1), lines_limit, 1):
            doc_changed_lines_1.append('')

    if len(doc_changed_lines_2) < lines_limit:
        for x in range(len(doc_changed_lines_2), lines_limit, 1):
            doc_changed_lines_2.append('')

    return doc_changed_lines_1, doc_changed_lines_2


def remove_date(doc_changed_lines_1, doc_changed_lines_2):
    removed_date_lines_1 = []
    removed_date_lines_2 = []
    date_pattern = re.compile(r"(20[0-9]{2}[/._-]+[0-9]{1,2}[/._-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+20[0-9]{2}"
                              r"|[0-9]{1,2}[/._-]+[0-9]{1,2}[/._-]+(2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+[0-9]{1,2}"
                              r"|[0-9]{1,2}[/._,\s-]+(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/._,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}[/.\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(20[0-9]{2}|2[0-4]|1[0-9])[/._,\s-]+(january|february|march|april|may|june|july|august|september|october|november|december)[/._,\s-]+[0-9]{1,2}"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+[0-9]{1,2}[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|(january|february|march|april|may|june|july|august|september|october|november|december)[/.,\s-]+(20[0-9]{2}|2[0-4]|1[0-9])"
                              r"|[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|)"
                              r"|[0-9]{1,2}:[0-9]{1,2}(\s[pa]m|))"
                              , flags=re.IGNORECASE)

    for line in doc_changed_lines_1:
        removed_date_lines_1.append(date_pattern.sub("", line).replace(" ", "").strip())
    for line in doc_changed_lines_2:
        removed_date_lines_2.append(date_pattern.sub("", line).replace(" ", "").strip())

    removed_date_lines_1.sort()
    removed_date_lines_2.sort()
    return removed_date_lines_1, removed_date_lines_2


def check_revision(doc_changed_lines_1, doc_changed_lines_2):
    revision_status_1 = []
    revision_status_2 = []

    revision_pattern = re.compile(r'(copyright|document created|creation date|date[\s:_]+|www.|http|created at'
                                  r'|downloaded|printed|phone|e-mail|as of|further information|subject to modifications'
                                  r'|rev:|rev\.|revised|generated|subject to change|without notice|updated|phone[\s:+]+'
                                  r'|technical changes reserved|issued:|©|all rights reserved|fax[\s:+]+|date:)'
                                  , flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):

        match1 = revision_pattern.findall(line1)
        match2 = revision_pattern.findall(line2)

        if match1:
            revision_status_1.append("Found")
        elif not match1 and line1 != '':
            revision_status_1.append("Not Found")
        else:
            revision_status_1.append("Found")

        if match2:
            revision_status_2.append("Found")
        elif not match2 and line2 != '':
            revision_status_2.append("Not Found")
        else:
            revision_status_1.append("Found")

    if list(set(revision_status_1)) == list(set(revision_status_2)) == ['Found']:
        return "Found"
    else:
        return 'Not Found'


def check_reach(doc_changed_lines_1, doc_changed_lines_2):
    reach_status_1 = 'Not Found'
    reach_status_2 = 'Not Found'

    reach_pattern = re.compile(r'((ec) no|candidate|reach|svhc|substance|article 59|echa'
                               r'|\([0-9]{1,2}[\s]*(jan|feb|mar|apr|may|june|jun|july|jul|aug|sept|sep|oct|nov|dec)[\s]*20[0-9]{2}\))',
                               flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = reach_pattern.findall(line1)
        match2 = reach_pattern.findall(line2)
        if match1:
            reach_status_1 = "Found"
        if match2:
            reach_status_2 = "Found"
    if reach_status_1 == 'Found' or reach_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def check_rohs(doc_changed_lines_1, doc_changed_lines_2):
    reach_status_1 = 'Not Found'
    reach_status_2 = 'Not Found'

    reach_pattern = re.compile(r'rohs',
                               flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = reach_pattern.findall(line1)
        match2 = reach_pattern.findall(line2)
        if match1:
            reach_status_1 = "Found"
        if match2:
            reach_status_2 = "Found"
    if reach_status_1 == 'Found' or reach_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def check_lc(doc_changed_lines_1, doc_changed_lines_2):
    lc_status_1 = 'Not Found'
    lc_status_2 = 'Not Found'

    lc_pattern = re.compile(r'Obsolete|Discontinued|Last time buy|Not Recommended for new design|nrnd'
                            r'|Withdrawn|EOL|LTB|Discontinu|Not Recommend|End of life|Not for new design'
                            r'|Inactive|End-of-Life|Not Active|end of service|end-of-service', flags=re.IGNORECASE)

    for line1, line2 in zip(doc_changed_lines_1, doc_changed_lines_2):
        match1 = lc_pattern.findall(line1)
        match2 = lc_pattern.findall(line2)
        if match1:
            lc_status_1 = "Found"
        if match2:
            lc_status_2 = "Found"
    if lc_status_1 == 'Found' or lc_status_2 == 'Found':
        return "Found"
    else:
        return 'Not Found'


def assign_status(compare_status, reach_status, lc_status, rohs_status, revision_status, num_lines_1, num_lines_2):
    check_change = []
    if num_lines_1 == num_lines_2 and compare_status != "Equal":
        if revision_status == 'Found':
            check_change.append('Check Revision')

    if lc_status == 'Found':
        check_change.append('Check Lc')

    if reach_status == 'Found':
        check_change.append('Check Reach')

    if rohs_status == 'Found':
        check_change.append('Check Rohs')

    check_change.sort()
    check_change_final = "|".join(check_change)

    if compare_status == 'Equal' and check_change_final == '':
        compare_status_final = 'Equal Manual'
    elif compare_status != 'Equal' and check_change_final == 'Check Revision':
        compare_status_final = 'Equal Manual'
    elif compare_status == 'Equal' and check_change_final != '':
        compare_status_final = 'Equal Parts'
    else:
        compare_status_final = 'Not Equal Manual'
    return compare_status_final, check_change_final


def assign_trust_level(compare_status, text1, text2, num_pages_1, num_pages_2, num_images_1, num_images_2):
    comment1, comment2, comment3, comment4 = '', '', '', ''

    '''Image change'''
    if num_images_1 != num_images_2:
        comment3 = "image change"
    else:
        comment3 = ''

    '''unsearchable'''
    if len(text1) < num_pages_1 * 300 or len(text2) < num_pages_2 * 300:
        comment4 = 'suspected unsearchable'
    else:
        comment4 = ''

    'Deducing accuracy percent'
    trust_level = 'None'
    if compare_status != 'Not Equal Manual':
        trust_level = 100
        if 0 < abs(len(text1) - len(text2)) <= 50:
            trust_level -= 10
            comment1 = 'different number of characters'
        elif abs(len(text1) - len(text2)) > 50:
            trust_level -= 50
            comment1 = 'different number of characters'

        if num_pages_2 != num_pages_1:
            trust_level -= 50
            comment2 = 'different number of pages'

        if comment4 == 'suspected unsearchable':
            trust_level = 0

    comment_list = [comment1, comment2, comment3, comment4]
    comment_list = list(set(comment_list))
    comment_list.sort()

    return trust_level, "|".join(comment_list)


def main(first_columns: str):
    """some variables"""
    custom_lines_limit = 20
    revision_status = 'Found'
    result_row = []
    four_two_lines_list = []
    first_columns = first_columns.strip()
    '''Assign link values'''
    link1 = first_columns.split('\t')[3].strip()
    link2 = first_columns.split('\t')[4].strip()
    first_columns = first_columns.removesuffix('\n')

    try:
        '''Get  number of pages in the document'''
        num_pages_1 = count_of_pages(link1)
        num_pages_2 = count_of_pages(link2)

        if num_pages_1 <= 101 or num_pages_2 <= 101:
            '''Extract all text from document'''
            doc_all_text_1 = fitz_extract_all_words(link1).lower()
            doc_all_text_2 = fitz_extract_all_words(link2).lower()
            
            if  "Please wait...".lower() in doc_all_text_1 or "Please wait...".lower() in doc_all_text_2:
                result_row.append(first_columns + '\t' + 'Reject\tIPC')
                return '\t'.join(result_row)

            '''Get  number of images in the document'''
            num_images_1 = count_of_images(link1)
            num_images_2 = count_of_images(link2)

            '''Compare lines'''
            changed_lines_1, changed_lines_2, num_lines_1, num_lines_2, compare_status = compare_lines(doc_all_text_1,
                                                                                                       doc_all_text_2,
                                                                                                       custom_lines_limit)

            '''Arranging output'''
            changed_lines_1.sort()
            changed_lines_2.sort()

            # fill blanks
            changed_lines_1, changed_lines_2 = arrange_lines(changed_lines_1, changed_lines_2, custom_lines_limit)
            for changed_line_1, changed_line_2 in zip(changed_lines_1, changed_lines_2):
                four_two_lines_list.append(changed_line_1.strip())
                four_two_lines_list.append(changed_line_2.strip())

            if compare_status != "Equal":
                revision_status = check_revision(changed_lines_1, changed_lines_2)

            reach_status = check_reach(changed_lines_1, changed_lines_2)
            lc_status = check_lc(changed_lines_1, changed_lines_2)
            rohs_status = check_rohs(changed_lines_1, changed_lines_2)

            compare_status, check_change = assign_status(compare_status, reach_status, lc_status, rohs_status
                                                         , revision_status, num_lines_1, num_lines_2)

            trust_level, comments = assign_trust_level(compare_status, doc_all_text_1, doc_all_text_2, num_pages_1,
                                                       num_pages_2, num_images_1, num_images_2)

            '''Assign result values'''
            result_row.append(first_columns + '\t' + 'Done')
            result_row.append(compare_status + '\t' + check_change + '\t' + str(trust_level))
            result_row.append(comments.replace("||", "|").removesuffix("|").removeprefix("|"))
            result_row.append(str(num_pages_1) + '\t' + str(num_pages_2) + '\t' + str(num_pages_1 == num_pages_2))
            result_row.append(str(num_images_1) + '\t' + str(num_images_2))
            result_row.append(str(len(doc_all_text_1)) + '\t' + str(len(doc_all_text_2)) + '\t' + str(
                len(doc_all_text_2) == len(doc_all_text_1)))
            result_row.append(str(len(doc_all_text_1) / num_pages_1) + '\t' + str(len(doc_all_text_2) / num_pages_2))
            result_row.append(str(num_lines_1) + '\t' + str(num_lines_2))
            result_row.append("\t".join(four_two_lines_list))

            return '\t'.join(result_row)

        else:
            result_row.append(first_columns + '\t' + 'Reject\tPage limit exceeded')
            return '\t'.join(result_row)

    except Exception as E:
        result_row = [first_columns + '\t' + 'Error', str(E)]
        return '\t'.join(result_row)
    
  
if __name__ == "__main__":

    input_path = sys.argv[1]
    output_path = sys.argv[2]
    mode = sys.argv[3]

    with open(input_path, "r") as input_file:
        links_list = input_file.read()
        links_list = json.loads(links_list)

    with open(output_path, "a", encoding='utf8') as output_file:
        pass    

    links_list = list(links_list.values())[:-1]

    if mode == "normal":
        for link in links_list:
            with open(output_path, "a", encoding='utf8') as output_file:
                output_file.write(main(link))
                output_file.write("\n")

    elif mode == "fast":
        one_time_count = 100
        with concurrent.futures.ProcessPoolExecutor(max_workers=7) as executor1:
            for i in range(1, len(links_list), one_time_count):
                batch_links = links_list[i:i + one_time_count]
                results = executor1.map(main, batch_links)
                for result in results:
                    with open(output_path, 'a', encoding='utf8') as of:
                        of.write(result)
                        of.write('\n')
                        print("Done")
        
        