:root {
  /* Brand Colors */
  --brand-primary: #FFC928;
  --brand-primary-strong: #FFB800;
  --brand-primary-weak: #FFE9A6;
  
  /* Neutral Colors */
  --neutral-bg: #F7F7F8;
  --neutral-surface: #FFFFFF;
  --neutral-surface-alt: #F3F4F6;
  --neutral-border: rgba(0,0,0,0.08);
  --neutral-muted: #8B8D97;
  --neutral-text: #161619;
  --neutral-text-soft: #40424A;
  --neutral-icon: #1C1B29;
  
  /* Status Colors */
  --status-success: #22C55E;
  --status-warning: #F59E0B;
  --status-danger: #EF4444;
  --status-info: #0EA5E9;
  
  /* Chart Colors */
  --chart-bar-1: #111113;
  --chart-bar-2: #6B6F76;
  --chart-bar-accent: #FFC928;
  --chart-line: #1C1B29;
  --chart-grid: rgba(0,0,0,0.06);
  --chart-dot: #FFC928;
  
  /* Elevation Colors */
  --elevation-shadow: rgba(17, 17, 19, 0.06);
  --elevation-shadow-strong: rgba(17, 17, 19, 0.10);
}
body {
    font-family: 'Nata Sans';
    font-weight: bold;
    margin: 0;
    height: 100vh;
    display: flex;
    background: var(--neutral-bg);
}
.side-bar {
  width: 100px;
  background: var(--neutral-surface-alt);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.side-bar .tool-category {
  color: #1C1B29;
  font-size: 24px;
  margin: 20px 0;
  cursor: pointer;
  background-color: var(--neutral-surface-alt);
  border-radius: 100%;
  padding: 10px;
}

.tool-card {
  height: 200px;
  background-color: var(--neutral-surface-alt);
  padding: 20px;
}
.tool-card h3 {
  display: flex;
  color: #1C1B29;
  font-size: 18px;
  margin: 10px 0;
  cursor: pointer;
  background-color: var(--brand-primary);
  border-radius: 10px;
}

.main-content {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.default-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #1C1B29;
  font-size: 18px;
  margin: 10px 0;
  cursor: pointer;
  background-color: var(--neutral-surface-alt);
  border-radius: 10px;
}

.selection-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #1C1B29;
  font-size: 18px;
  margin: 10px 0;
  cursor: pointer;
  background-color: var(--neutral-surface-alt);
  border-radius: 10px;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}